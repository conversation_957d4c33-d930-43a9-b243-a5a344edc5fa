using System.Security.Authentication.ExtendedProtection;
using HeroLogin.Services;

namespace HeroLogin.Core.Actors
{
    public class Builder
    {
        public static byte[] BuildLoginSuccessResponse(string account, string password)
        {
            using var w = new PacketWriter();
            w.Write4(0);
            w.Write2(1000);
            w.WriteStringWithLength(account);
            w.WriteStringWithLength(password);
            var randStr = "YHERO";
            var time = DateTime.Now.ToString("yyyyMMddHHmmss");
            w.WriteStringWithLength(randStr + time);
            w.Write2(10);
            return w.GetBytes();
        }

        public static byte[] BuildServerListResponse(List<ServerCluster> serverClusters)
        {
            using var w = new PacketWriter();

            // Số lượng cluster
            w.Write2(serverClusters.Count);

            foreach (var cluster in serverClusters)
            {
                // ID của cluster
                w.Write2(cluster.ID);
                w.WriteStringWithLength(cluster.ClusterName);
                w.Write4(0);
                w.Write2(0);
                w.Write2(cluster.Channels.Count);
                w.Write1(1); // Status online
                w.Write1(0);

                foreach (var channel in cluster.Channels)
                {
                    // ID của kênh
                    w.Write2(channel.ServerID);

                    // Tên của kênh
                    w.WriteStringWithLength(channel.ServerName);

                    // Trạng thái của kênh
                    //w.Write1(channel.Status ? (byte)1 : (byte)0);
                    w.Write1(1);
                    // Số người chơi hiện tại và tối đa
                    //w.Write2(channel.CurrentPlayers);
                    w.Write2(0);
                    w.Write2(channel.MaximumOnline);

                    // Badge của kênh
                    //w.Write4(channel.Badge);
                }
            }

            return w.GetBytes();
        }
        
        public static byte[] BuildSelectServerReponse(int clusterId,ServerChannel channel)
        {
            Logger.Instance.Info($"BuildSelectServerReponse: {channel.ServerIP} - {channel.GameServerPort} - {clusterId} - {channel.ServerID} - {channel.ServerName}");
            using var w = new PacketWriter();
            w.WriteStringWithLength(channel.ServerIP);
            w.Write4(channel.GameServerPort);
            w.Write4(clusterId);
            w.Write4(channel.ServerID);
            w.WriteStringWithLength(channel.ServerName);
            return w.GetBytes();
        }
    }
}
