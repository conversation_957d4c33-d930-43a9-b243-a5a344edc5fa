syntax = "proto3";

option csharp_namespace = "HeroLogin.Protos";

package login;

// D<PERSON><PERSON> vụ xác thực tài khoản
service LoginAuth {
  // Xác thực token đăng nhập
  rpc VerifyToken (VerifyTokenRequest) returns (VerifyTokenResponse);

  // Đăng ký GameServer với LoginServer
  rpc RegisterGameServer (RegisterGameServerRequest) returns (RegisterGameServerResponse);

  // Cập nhật trạng thái của GameServer
  rpc UpdateGameServerStatus (UpdateGameServerStatusRequest) returns (UpdateGameServerStatusResponse);

  rpc TransmitMessage (stream TransmitMessageRequest) returns (stream TransmitMessageResponse);

  rpc ValidateAccountLogin (ValidateAccountLoginRequest) returns (ValidateAccountLoginResponse);
}

message ValidateAccountLoginRequest {
  string account_id = 1;
  string password = 2;
  int32 cluster_id = 3;
  int32 server_id = 4;
  string user_ip = 5;
  int32 user_port = 6;
  string lan_ip = 7;
  int32 lan_port = 8;
}

message ValidateAccountLoginResponse {
  bool is_valid = 1;
  string error_message = 2;
  string account_id = 3;
  string login_ip = 4;
  string login_port = 5;
}


message TransmitMessageRequest {
  int32 cluster_id = 1;
  int32 server_id = 2;
  string message = 3;
}

message TransmitMessageResponse {
  bool success = 1;
  string message = 2;
}

// Yêu cầu xác thực token
message VerifyTokenRequest {
  string account_id = 1;
  string token = 2;
  int32 cluster_id = 3;
  int32 server_id = 4;
}

// Phản hồi xác thực token
message VerifyTokenResponse {
  bool is_valid = 1;
  string account_id = 2;
  string error_message = 3;
}

// Yêu cầu đăng ký GameServer
message RegisterGameServerRequest {
  int32 cluster_id = 1;
  int32 server_id = 2;
  string server_name = 3;
  string server_ip = 4;
  int32 server_port = 5;
  int32 grpc_port = 6;
}

// Phản hồi đăng ký GameServer
message RegisterGameServerResponse {
  bool success = 1;
  string message = 2;
}

// Yêu cầu cập nhật trạng thái GameServer
message UpdateGameServerStatusRequest {
  int32 cluster_id = 1;
  int32 server_id = 2;
  int32 online_count = 3;
  bool is_online = 4;
}

// Phản hồi cập nhật trạng thái GameServer
message UpdateGameServerStatusResponse {
  bool success = 1;
  string message = 2;
}
